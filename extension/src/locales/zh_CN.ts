export const zhCNMessages = {
  // Basic Extension Info
  extensionName: 'Mysta',
  extensionDescription: 'Mysta - 让AI替您操作网页',

  // Navigation and UI
  viewProfile: '查看资料',
  conversations: '对话',
  newChat: '新建对话',
  allChats: '所有对话',
  user: '用户',
  language: '语言',
  english: 'English',
  chinese: '中文',
  settings: '设置',
  logout: '登出',
  workflowDetail: '任务详情',

  // Actions
  save: '保存',
  cancel: '取消',
  delete: '删除',
  close: '关闭',
  ok: '确定',
  notNow: '稍后',
  getStarted: '开始使用',

  // API Key Management
  setApiKey: '设置API密钥',
  enterApiKey: '输入 $1 API密钥',
  apiKeyDisabled: 'API密钥已禁用',
  goToGetApiKey: '去获取API密钥',
  enableApiKey: '启用您的Mysta API密钥',
  apiKeyRequired: '您需要登录获取API密钥才能使用完整功能',

  // Authentication
  signInWithMysta: '使用Mysta网页版登录',
  mystaAccountDetected: '检测到Mysta账户',

  // Chat Interface
  thinking: '思考中',
  typeMessage: '输入消息...',
  send: '发送',
  attachFile: '附加文件',
  recordAudio: '录制语音',
  workflowMode: '任务模式',

  // Error Messages
  insufficientCredits: '余额不足。请为您的账户充值。',
  insufficientCreditsTip: '余额不足',
  rechargeTip: '充值',
  errorOccurred: '发生错误。请重试。',

  // TopBanner
  joinTelegramGroup: '🎉 加入我们的电报群获得$5积分！',
  cannotSaveForCurrentSite: '无法为当前网站保存。',
  noMessagesToSave: '没有消息可保存。',
  failedToSaveToSiteMemory: '保存到网站记忆失败。',

  // Success Messages
  savedToSiteMemorySuccessfully: '成功保存到网站记忆！',

  // Tool Execution Status
  executing: '执行中',
  executed: '已执行',
  running: '运行中',
  error: '错误',

  // Home Page
  letAiRunTheWeb: '让AI替您操作网页',
  askAnything: '随心所问\n任意而行',
  startTyping: '开始输入 — 您的AI助手已就位',
  privacyDisclaimer: 'Mysta AI助手可能产生不准确的信息。您的数据保持私密。',

  // Conversation Management
  deleteConversation: '删除对话',
  deleteConversationConfirm: '您确定要删除此对话吗？此操作无法撤销。',
  newChatItem: '新对话',

  // Site Memory
  saveToSiteMemory: '保存到网站记忆',
  saveToSiteMemoryTitle: '保存到网站记忆',

  // Tooltips
  tooltipNewChat: '新建对话',
  tooltipClose: '关闭',
  tooltipDelete: '删除',
  tooltipConversations: '对话列表',
  tooltipUser: '用户',
  tooltipSaveToSiteMemory: '保存到网站记忆',

  // Prompt Templates
  summarizeElonTweet: '总结埃隆·马斯克的最新推文。',
  postTweet: "发布一条推文：'mysta太棒了'。",
  searchLinkedIn: '在我的LinkedIn上搜索MystaAI。',

  // Copy Functionality
  copyToClipboard: '复制到剪贴板',
  copied: '已复制！',

  // File Upload
  selectFile: '选择文件',
  uploadFile: '上传文件',

  // Status
  idle: '空闲',
  waiting: '等待中',
  processing: '处理中',

  // Common Actions
  edit: '编辑',
  share: '分享',
  export: '导出',
  import: '导入',
  refresh: '刷新',
  retry: '重试',

  // Validation Messages
  fieldRequired: '此字段为必填项',
  invalidEmail: '请输入有效的邮箱地址',
  invalidUrl: '请输入有效的URL',

  // Loading States
  loading: '加载中...',
  pleaseWait: '请稍候...',

  // Search
  search: '搜索',
  searchPlaceholder: '搜索对话...',
  noResults: '未找到结果',

  // Time/Date
  now: '现在',
  today: '今天',
  yesterday: '昨天',
  thisWeek: '本周',
  lastWeek: '上周',

  // Permissions
  permissionDenied: '权限被拒绝',
  accessRestricted: '访问受限',

  // Connection
  connectionError: '连接错误',
  networkError: '网络错误',
  retryConnection: '重新连接',

  // Features
  comingSoon: '即将推出',
  betaFeature: '测试功能',
  experimental: '实验性功能',

  // Reasoning
  thoughtProcess: '思考过程',

  // Model Settings
  setApiKeyTooltip: '设置API密钥',
  modelName: 'Mysta',
} as const;
